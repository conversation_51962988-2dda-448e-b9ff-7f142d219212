#!/usr/bin/env tsx

/**
 * Example usage of different AI providers for web scraping
 *
 * This example demonstrates how to use both Ollama and DeepSeek V3
 * providers for extracting product information from e-commerce websites.
 */

import {
    createOllamaScraper,
    createDeepSeekScraper,
    createAiGenericScraper
} from '../src/server/scraper/implementations/listings/aiGeneric'
import { testAiConnection } from '../src/server/scraper/utils/aiScraper'
import { type AiScraperConfig } from '../src/server/scraper/schemas'

async function main() {
    console.log('🤖 AI Providers Example\n')

    // Test URL to scrape
    const testUrl = 'https://www.3djake.de/filament/pet-filament'

    // Example 1: Using Ollama provider
    console.log('=== Example 1: Ollama Provider ===')
    
    const ollamaConfig: AiScraperConfig = {
        provider: 'ollama',
        endpoint: 'http://localhost:11434',
        model: 'llama3.2',
        temperature: 0.1,
        maxTokens: 4096,
        instructions: 'Extract 3D printing filament products with all specifications.'
    }

    try {
        console.log('Testing Ollama connection...')
        const ollamaConnected = await testAiConnection(ollamaConfig)
        
        if (ollamaConnected) {
            console.log('✅ Ollama connection successful')
            
            // Create scraper using the generic function
            const ollama1 = createAiGenericScraper(ollamaConfig)
            console.log(`Created scraper: ${ollama1.name}`)
            
            // Or use the convenience function
            const ollama2 = createOllamaScraper({
                endpoint: 'http://localhost:11434',
                model: 'llama3.2'
            })
            console.log(`Created scraper: ${ollama2.name}`)
            
        } else {
            console.log('❌ Ollama connection failed')
            console.log('Make sure Ollama is running: ollama serve')
            console.log('And the model is available: ollama pull llama3.2')
        }
    } catch (error) {
        console.error('Error with Ollama:', error)
    }

    console.log('\n=== Example 2: DeepSeek V3 Provider ===')
    
    // Example 2: Using DeepSeek V3 provider
    const deepseekApiKey = process.env.DEEPSEEK_API_KEY || 'your-api-key-here'
    
    const deepseekConfig: AiScraperConfig = {
        provider: 'deepseek',
        endpoint: 'https://api.deepseek.com',
        model: 'deepseek-v3',
        temperature: 0.1,
        maxTokens: 4096,
        apiKey: deepseekApiKey,
        instructions: 'Extract 3D printing filament products with all specifications.'
    }

    try {
        if (deepseekApiKey === 'your-api-key-here') {
            console.log('⚠️  DeepSeek API key not provided')
            console.log('Set DEEPSEEK_API_KEY environment variable to test DeepSeek provider')
        } else {
            console.log('Testing DeepSeek connection...')
            const deepseekConnected = await testAiConnection(deepseekConfig)
            
            if (deepseekConnected) {
                console.log('✅ DeepSeek connection successful')
                
                // Create scraper using the generic function
                const deepseek1 = createAiGenericScraper(deepseekConfig)
                console.log(`Created scraper: ${deepseek1.name}`)
                
                // Or use the convenience function
                const deepseek2 = createDeepSeekScraper(deepseekApiKey, {
                    temperature: 0.2
                })
                console.log(`Created scraper: ${deepseek2.name}`)
                
            } else {
                console.log('❌ DeepSeek connection failed')
                console.log('Check your API key and endpoint configuration')
            }
        }
    } catch (error) {
        console.error('Error with DeepSeek:', error)
    }

    console.log('\n=== Example 3: Provider Comparison ===')
    
    // Example 3: Compare different providers for the same task
    const providers = [
        {
            name: 'Ollama (Local)',
            config: ollamaConfig,
            available: ollamaConfig.endpoint.includes('localhost')
        },
        {
            name: 'DeepSeek V3 (Cloud)',
            config: deepseekConfig,
            available: deepseekApiKey !== 'your-api-key-here'
        }
    ]

    for (const provider of providers) {
        console.log(`\n--- Testing ${provider.name} ---`)
        
        if (!provider.available) {
            console.log(`⏭️  Skipping ${provider.name} (not configured)`)
            continue
        }

        try {
            const connected = await testAiConnection(provider.config)
            if (connected) {
                console.log(`✅ ${provider.name} is ready`)
                
                // You could run actual scraping here:
                // const scraper = createAiGenericScraper(provider.config)
                // const products = await scraper.scrapeUrl(testUrl)
                // console.log(`Found ${products.length} products`)
                
            } else {
                console.log(`❌ ${provider.name} connection failed`)
            }
        } catch (error) {
            console.error(`Error with ${provider.name}:`, error)
        }
    }

    console.log('\n🎉 Provider examples completed!')
    console.log('\nNext steps:')
    console.log('1. Configure your preferred provider')
    console.log('2. Use the provider in your scraping workflows')
    console.log('3. Switch providers based on your needs (local vs cloud, cost, performance)')
}

// Run the example
if (require.main === module) {
    main().catch(console.error)
}

export { main as runAiProvidersExample }
