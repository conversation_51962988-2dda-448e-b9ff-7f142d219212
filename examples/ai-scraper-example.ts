#!/usr/bin/env tsx

/**
 * Example usage of the AI-powered generic scraper
 *
 * This example demonstrates how to use the AI scraper to extract
 * product information from various e-commerce websites.
 */

import { createAiGenericScraper } from '../src/server/scraper/implementations/listings/aiGeneric'
import { testAiConnection } from '../src/server/scraper/utils/aiScraper'
import { type AiScraperConfig } from '../src/server/scraper/schemas'

async function main() {
    console.log('🤖 AI Scraper Example\n')

    // Configuration for the AI scraper (using provider defaults)
    const config: AiScraperConfig = {
        provider: 'ollama',
        instructions: `
            Extract 3D printing filament products from this webpage.
            Focus on products with clear specifications.
            Include material type, weight, color, and price information.
            Only extract products that are clearly 3D printing filaments.
        `
    }

    // Test URLs to scrape
    const testUrls = [
        'https://www.3djake.de/filament/pet-filament'
        // Add more URLs as needed
    ]

    try {
        // Step 1: Test Ollama connection
        console.log('1. Testing Ollama connection...')
        const isConnected = await testAiConnection(config)

        if (!isConnected) {
            console.error('❌ Cannot connect to Ollama. Please ensure:')
            console.error('   - Ollama is running: ollama serve')
            console.error('   - Model is available: ollama pull llama3.2')
            return
        }

        console.log('✅ Ollama connection successful\n')

        // Step 2: Create scraper instance
        console.log('2. Creating AI scraper instance...')
        const scraper = createAiGenericScraper(config)
        console.log('✅ Scraper created\n')

        // Step 3: Scrape each URL
        for (const url of testUrls) {
            console.log(`3. Scraping: ${url}`)
            console.log('   Please wait, this may take 30-60 seconds...\n')

            const startTime = Date.now()

            try {
                const products = await scraper.scrapeUrl(url, {
                    headless: true,
                    timeout: 60000, // 60 second timeout
                    retries: 2,
                    delayMin: 1000,
                    delayMax: 3000
                })

                const endTime = Date.now()
                const duration = ((endTime - startTime) / 1000).toFixed(2)

                console.log(`✅ Scraping completed in ${duration}s`)
                console.log(`📦 Found ${products.length} products:\n`)

                if (products.length === 0) {
                    console.log('   No products found. Possible reasons:')
                    console.log('   - Page has no 3D printing filaments')
                    console.log('   - AI model needs better instructions')
                    console.log('   - Page structure is too complex\n')
                } else {
                    // Display extracted products
                    products.forEach((product, index) => {
                        console.log(`   ${index + 1}. ${product.name}`)
                        console.log(`      Brand: ${product.brand}`)
                        console.log(`      Material: ${product.materialType}`)
                        console.log(`      Color: ${product.color}`)
                        console.log(`      Weight: ${product.weight}g`)

                        if (product.currentPrice > 0) {
                            console.log(
                                `      Price: ${(product.currentPrice / 100).toFixed(2)} ${product.currency}`
                            )
                        } else {
                            console.log(`      Price: Not available`)
                        }

                        console.log(`      URL: ${product.identifier}`)
                        console.log('')
                    })
                }
            } catch (error) {
                console.error(`❌ Failed to scrape ${url}:`)
                console.error(
                    `   ${error instanceof Error ? error.message : error}\n`
                )
            }
        }

        // Step 4: Example of batch scraping
        if (testUrls.length > 1) {
            console.log('4. Example: Batch scraping multiple URLs...')

            try {
                const batchStartTime = Date.now()

                // Note: In a real application, you would use the service layer
                // This is just for demonstration
                const allProducts = []

                for (const url of testUrls) {
                    try {
                        const products = await scraper.scrapeUrl(url)
                        allProducts.push(...products)

                        // Add delay between requests
                        await new Promise((resolve) =>
                            setTimeout(resolve, 3000)
                        )
                    } catch (error) {
                        console.error(`   Failed to scrape ${url}: ${error}`)
                    }
                }

                const batchEndTime = Date.now()
                const batchDuration = (
                    (batchEndTime - batchStartTime) /
                    1000
                ).toFixed(2)

                console.log(`✅ Batch scraping completed in ${batchDuration}s`)
                console.log(`📦 Total products found: ${allProducts.length}\n`)
            } catch (error) {
                console.error(`❌ Batch scraping failed: ${error}\n`)
            }
        }

        console.log('🎉 Example completed successfully!')
        console.log('\nNext steps:')
        console.log('- Use the tRPC API endpoints for production usage')
        console.log('- Integrate with the database using aiScrapeAndSave')
        console.log('- Customize instructions for better extraction quality')
        console.log('- Monitor and adjust model parameters as needed')
    } catch (error) {
        console.error(
            '💥 Example failed:',
            error instanceof Error ? error.message : error
        )
        process.exit(1)
    }
}

// Helper function to demonstrate custom configurations
function createCustomConfigs(): AiScraperConfig[] {
    return [
        // High accuracy configuration with custom settings
        {
            provider: 'ollama',
            endpoint: 'http://localhost:11434', // Custom endpoint
            model: 'llama3.2', // Custom model
            temperature: 0.05, // Lower temperature for consistency
            maxTokens: 6144, // Higher token limit for detailed extraction
            instructions: `
                You are an expert at extracting 3D printing filament product information.
                Extract only high-quality, complete product data.
                Ensure all required fields are present and accurate.
                Focus on premium filament products with detailed specifications.
            `
        },

        // Fast extraction configuration with provider defaults
        {
            provider: 'ollama',
            model: 'mistral', // Faster model
            temperature: 0.2, // Slightly higher for speed
            maxTokens: 2048, // Lower token limit for speed
            instructions: `
                Quickly extract basic 3D printing filament information.
                Focus on name, brand, material type, and price.
                Skip products with incomplete information.
            `
        },

        // Provider defaults with custom instructions only
        {
            provider: 'ollama',
            instructions: `
                Extract only PETG and PLA filament products.
                Ignore other material types like ABS, TPU, etc.
                Focus on products with clear weight and color specifications.
                Prioritize products from well-known brands.
            `
        }
    ]
}

// Run the example
if (require.main === module) {
    main().catch(console.error)
}
