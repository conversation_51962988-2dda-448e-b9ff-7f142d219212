import { type ScrapeAndCreateParams } from '../schemas'
import { type ProductScraper } from '../types'
import { navigateWithStealth, safelyCloseResources } from '../browser'
import { type CreateProduct } from '~/server/product/schemas'
import { parsePrice } from '../utils/price'
import {
    simulateHumanBehavior,
    addRandomDelay,
    detectAntiBotMeasures,
    safelyGetText
} from '../utils/humanBehavior'
import { generateProductUrl } from '~/server/product/url'

const scrapeProduct = async ({
    product,
    options
}: ScrapeAndCreateParams): Promise<CreateProduct> => {
    const productUrl = generateProductUrl(product.store, product.identifier)

    // Navigate to the product page with stealth
    const { browser, context, page } = await navigateWithStealth(
        productUrl,
        options
    )

    try {
        // Simulate additional human-like behavior for Amazon (handles cookie consent, scrolling, etc.)
        await simulateHumanBehavior(page)

        // Safely get the price text with anti-bot detection
        const priceText = await safelyGetText(
            page,
            '#corePriceDisplay_desktop_feature_div .a-price',
            { timeout: options?.timeout }
        )

        if (!priceText) {
            // Check if we're being blocked by anti-bot measures
            if (await detectAntiBotMeasures(page)) {
                throw new Error('Access blocked - detected anti-bot measures')
            }

            throw new Error('Price element not found')
        }

        const currentPrice = parsePrice(priceText)

        return {
            ...product,
            currentPrice
        }
    } finally {
        // Safely close all resources
        await safelyCloseResources(page, context, browser, options)
    }
}

export const amazonScraper: ProductScraper = {
    name: 'amazon',
    scrape: async (params: ScrapeAndCreateParams) => {
        const scrapedProduct = await scrapeProduct(params)
        return scrapedProduct
    }
}
