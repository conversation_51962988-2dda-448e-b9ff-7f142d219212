import { type AiL<PERSON>ingScraper } from '../../types'
import { type CreateProduct } from '../../../product/schemas'
import { navigateWithStealth, safelyCloseResources } from '../../browser'
import { type ScraperOptions, type AiScraperConfig } from '../../schemas'
import { extractProductsWithAi, testAiConnection } from '../../utils/aiScraper'
import { addRandomDelay } from '../../utils/humanBehavior'

/**
 * Scrape any webpage using AI to extract product information
 */
async function scrapeUrl(
    url: string,
    config: AiScraperConfig,
    options?: ScraperOptions
): Promise<CreateProduct[]> {
    console.log(`[AI SCRAPER] Starting to scrape URL: ${url}`)

    // Test AI provider connection first
    const isConnected = await testAiConnection(config)
    if (!isConnected) {
        throw new Error(`Cannot connect to AI provider at ${config.endpoint}`)
    }

    // Navigate to the page with stealth
    const { browser, context, page } = await navigateWithStealth(url, options)

    try {
        // Wait for the page to load completely
        await page.waitForLoadState('networkidle', {
            timeout: options?.timeout || 30000
        })

        // Add random delay to appear more human-like
        await addRandomDelay(page, 1000, 3000)

        // Get the full HTML content
        const htmlContent = await page.content()

        console.log(
            `[AI SCRAPER] Extracted HTML content (${htmlContent.length} characters)`
        )

        // Use AI to extract product information
        const products = await extractProductsWithAi(htmlContent, config)

        console.log(
            `[AI SCRAPER] AI extracted ${products.length} products from ${url}`
        )

        return products
    } catch (error) {
        console.error(`[AI SCRAPER] Error scraping URL ${url}:`, error)
        throw error
    } finally {
        // Safely close all resources
        await safelyCloseResources(page, context, browser, options)
    }
}

/**
 * Create a new AI generic scraper instance
 */
export function createAiGenericScraper(
    config: AiScraperConfig
): AiListingScraper {
    return {
        name: `AI Generic Listing Scraper (${config.provider})`,
        config,
        scrapeUrl: (url: string, options?: ScraperOptions) =>
            scrapeUrl(url, config, options)
    }
}

/**
 * Default AI scraper configuration for quick setup
 * Note: endpoint, model, temperature and maxTokens are omitted to use provider defaults
 */
export const defaultAiScraperConfig: AiScraperConfig = {
    provider: 'ollama',
    instructions:
        'Focus on 3D printing filaments and materials. Extract all available product details including specifications.'
}

/**
 * Create an Ollama-based AI scraper with default configuration
 */
export function createOllamaScraper(
    overrides?: Partial<AiScraperConfig>
): AiListingScraper {
    const config: AiScraperConfig = {
        ...defaultAiScraperConfig,
        provider: 'ollama',
        ...overrides
    }
    return createAiGenericScraper(config)
}

/**
 * Create a DeepSeek V3-based AI scraper with default configuration
 */
export function createDeepSeekScraper(
    apiKey: string,
    overrides?: Partial<AiScraperConfig>
): AiListingScraper {
    const config: AiScraperConfig = {
        provider: 'deepseek',
        instructions:
            'Focus on 3D printing filaments and materials. Extract all available product details including specifications.',
        apiKey,
        ...overrides
    }
    return createAiGenericScraper(config)
}

/**
 * Scrape multiple URLs with the same AI configuration
 */
export async function scrapeMultipleUrls(
    urls: string[],
    config: AiScraperConfig,
    options?: ScraperOptions
): Promise<CreateProduct[]> {
    console.log(`[AI SCRAPER] Starting to scrape ${urls.length} URLs`)

    const allProducts: CreateProduct[] = []

    for (const url of urls) {
        try {
            const products = await scrapeUrl(url, config, options)
            allProducts.push(...products)

            // Add delay between URLs to avoid being blocked
            const delay = Math.floor(Math.random() * 5000) + 3000
            console.log(`[AI SCRAPER] Waiting ${delay}ms before next URL...`)
            await new Promise((resolve) => setTimeout(resolve, delay))
        } catch (error) {
            console.error(`[AI SCRAPER] Error scraping URL ${url}:`, error)
            // Continue with other URLs even if one fails
        }
    }

    console.log(
        `[AI SCRAPER] Completed scraping ${urls.length} URLs. Total products: ${allProducts.length}`
    )
    return allProducts
}

/**
 * Batch scrape with different configurations for different URLs
 */
export async function batchScrapeWithConfigs(
    urlConfigs: Array<{ url: string; config: AiScraperConfig }>,
    options?: ScraperOptions
): Promise<CreateProduct[]> {
    console.log(
        `[AI SCRAPER] Starting batch scrape of ${urlConfigs.length} URL configurations`
    )

    const allProducts: CreateProduct[] = []

    for (const { url, config } of urlConfigs) {
        try {
            const products = await scrapeUrl(url, config, options)
            allProducts.push(...products)

            // Add delay between URLs
            const delay = Math.floor(Math.random() * 5000) + 3000
            await new Promise((resolve) => setTimeout(resolve, delay))
        } catch (error) {
            console.error(
                `[AI SCRAPER] Error in batch scrape for URL ${url}:`,
                error
            )
        }
    }

    console.log(
        `[AI SCRAPER] Completed batch scrape. Total products: ${allProducts.length}`
    )
    return allProducts
}
