import { type ListingScraper } from '../../types'
import { type CreateProduct } from '~/server/product/schemas'
import { navigateWithStealth, safelyCloseResources } from '../../browser'
import { type ScraperOptions } from '../../schemas'
import { Currency, MaterialType, Store, Location } from '@prisma/client'
import { parsePrice } from '../../utils/price'
import { addRandomDelay } from '../../utils/humanBehavior'

/**
 * Scraper for Prusa3D product listings
 */
export const prusa3dListingScraper: ListingScraper = {
    name: 'Prusa3D Listing Scraper',
    store: Store.prusa3d,

    /**
     * Get the URLs of Prusa3D filament listing pages to scrape
     */
    getListingUrls: () => ['https://www.prusa3d.com/de/kategorie/petg/'],

    /**
     * Scrape Prusa3D product listings to find new products
     */
    scrapeListings: async (
        options?: ScraperOptions
    ): Promise<CreateProduct[]> => {
        console.log(`[LISTINGS SCRAPER] Starting Prusa3D listings scraper`)
        const products: CreateProduct[] = []
        const urls = prusa3dListingScraper.getListingUrls()

        for (const url of urls) {
            try {
                const pageProducts = await scrapeListingPage(url, options)
                products.push(...pageProducts)

                // Add a delay between pages to avoid detection
                const delay = Math.floor(Math.random() * 5000) + 3000
                await new Promise((resolve) => setTimeout(resolve, delay))
            } catch (error) {
                console.error(
                    `[LISTINGS SCRAPER] Error scraping listing page ${url}:`,
                    error
                )
            }
        }

        console.log(
            `[LISTINGS SCRAPER] Completed scraping. Total products found: ${products.length}`
        )
        return products
    }
}

/**
 * Scrape a single Prusa3D listing page
 */
async function scrapeListingPage(
    url: string,
    options?: ScraperOptions
): Promise<CreateProduct[]> {
    console.log(`[LISTINGS SCRAPER] Starting to scrape listing page: ${url}`)
    const products: CreateProduct[] = []

    // Navigate to the listing page with stealth
    const { browser, context, page } = await navigateWithStealth(url, options)

    try {
        // Wait for product grid to load - using href attribute which is more stable
        await page.waitForSelector('a[href^="/de/produkt/"]', {
            timeout: options?.timeout || 30000
        })

        // Simple approach: scroll and click "Load more" button up to 3 times
        for (let i = 0; i < 3; i++) {
            console.log(`[LISTINGS SCRAPER] Scroll and load cycle ${i + 1}/3`)

            // Scroll to bottom
            await page.evaluate(() => {
                window.scrollTo(0, document.documentElement.scrollHeight)
            })

            // Wait for content to load
            await page.waitForTimeout(2000)
            await page.waitForLoadState('networkidle').catch(() => {})

            // Check for load more button
            const hasLoadMoreButton = await page.evaluate(() => {
                const buttons = Array.from(document.querySelectorAll('button'))
                const loadMoreBtn = buttons.find(
                    (btn) =>
                        btn.textContent &&
                        (btn.textContent.includes('Lade') ||
                            btn.textContent.includes('Load'))
                )
                if (loadMoreBtn) {
                    loadMoreBtn.click()
                    return true
                }
                return false
            })

            if (hasLoadMoreButton) {
                console.log(
                    `[LISTINGS SCRAPER] Clicked "Load more" button in cycle ${i + 1}`
                )
                // Wait for new content to load
                await page.waitForTimeout(2000)
                await page.waitForLoadState('networkidle').catch(() => {})
            } else {
                console.log(
                    `[LISTINGS SCRAPER] No "Load more" button found in cycle ${i + 1}`
                )
                break
            }

            // Add a delay between cycles
            await page.waitForTimeout(1000)
        }

        // After all scrolling and loading, process the products
        await processProductsOnPage(page, products)
        console.log(
            `[LISTINGS SCRAPER] Found ${products.length} products after scrolling and loading`
        )

        return products
    } finally {
        // Safely close all resources
        await safelyCloseResources(page, context, browser, options)
    }
}

/**
 * Process all products on the current page
 */
async function processProductsOnPage(
    page: any,
    products: CreateProduct[]
): Promise<void> {
    // Get all product cards - using href attribute which is more stable
    const productCards = await page.$$('a[href^="/de/produkt/"]')
    console.log(
        `[LISTINGS SCRAPER] Found ${productCards.length} product cards to process`
    )

    for (const card of productCards) {
        try {
            // Extract the product URL to use as identifier
            const productUrl = await card.getAttribute('href')
            if (!productUrl) continue

            // Extract the identifier from the URL
            const identifier = productUrl
                .replace('/de/produkt/', '')
                .replace(/\/$/, '')
            if (!identifier) continue

            // Extract full product name - using h2 element which is more stable
            const nameElement = await card.$('h2')
            const fullName = nameElement ? await nameElement.innerText() : ''
            if (!fullName) continue

            // Brand is always Prusament for Prusa3D store
            const brand = 'Prusament'

            // Get current price - using data-ge-price attribute which is more stable
            const priceElement = await card.$('[data-ge-price="true"]')
            const priceText = priceElement ? await priceElement.innerText() : ''
            if (!priceText) continue

            const currentPrice = parsePrice(priceText)

            // Check stock status - look for text containing "Vorrätig" (In Stock)
            // Using a more general approach since class names are dynamic
            // We could use this to filter out of stock items if needed in the future
            // const stockText = await card.evaluate((el: Element) => {
            //     // Look for any element containing stock information
            //     const stockEl = el.querySelector('svg[data-icon="truck-fast"]')
            //     if (stockEl) {
            //         // Get the parent element that might contain the text
            //         const parent = stockEl.closest('span')
            //         return parent ? parent.textContent : ''
            //     }
            //     return ''
            // })

            // Extract material type from the product name
            let materialType: MaterialType = MaterialType.PLA
            if (fullName.includes('PETG')) {
                materialType = MaterialType.PETG
            } else if (fullName.includes('ASA')) {
                materialType = MaterialType.ASA
            } else if (fullName.includes('TPU')) {
                materialType = MaterialType.TPU
            } else if (fullName.includes('NYLON')) {
                materialType = MaterialType.NYLON
            } else if (fullName.includes('ABS')) {
                materialType = MaterialType.ABS
            }

            // Extract weight from product name (usually in format like "1kg")
            let weight = 1000 // Default to 1kg (1000g)

            // Match weight patterns
            const kgMatch = fullName.match(/(\d+(?:[.,]\d+)?)(?:\s*)kg/i)
            const gMatch = fullName.match(/(\d+(?:[.,]\d+)?)(?:\s*)g/i)

            if (kgMatch && kgMatch[1]) {
                const weightStr = kgMatch[1]
                    .replace(/\.(?=\d{3})/g, '') // Remove dots used as thousands separators
                    .replace(',', '.') // Replace comma with dot for decimal point
                weight = Math.round(parseFloat(weightStr) * 1000)
            } else if (gMatch && gMatch[1]) {
                const weightStr = gMatch[1]
                    .replace(/\.(?=\d{3})/g, '') // Remove dots used as thousands separators
                    .replace(',', '.') // Replace comma with dot for decimal point
                weight = Math.round(parseFloat(weightStr))
            }

            // Extract color from product name
            let color = 'Unknown'

            // For Prusament products, the color typically comes after the material type
            // Example: "Prusament PETG Carbon Fiber Black 1kg"
            const colorMatch = fullName.match(
                /(PLA|PETG|ABS|TPU|ASA|NYLON)\s+(.*?)(?=\s+\d+(?:[.,]\d+)?(?:\s*)(?:kg|g))/i
            )

            if (colorMatch && colorMatch[2]) {
                color = colorMatch[2].trim()
                // Capitalize the first letter
                color = color.replace(/^\w/, (c) => c.toUpperCase())
            }

            // Create product object
            products.push({
                name: fullName,
                identifier,
                store: Store.prusa3d,
                brand,
                currentPrice,
                currency: Currency.EUR,
                materialType,
                weight,
                color,
                locations: [Location.Germany]
            })

            // Add a small delay between processing products
            await addRandomDelay(page, 100, 300)
        } catch (error) {
            console.error(
                `[LISTINGS SCRAPER] Error extracting product data:`,
                error
            )
        }
    }
}
