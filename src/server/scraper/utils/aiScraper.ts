import { type AiScraperConfig } from '../schemas'
import { type CreateProduct } from '../../product/schemas'
import { Currency, MaterialType, Store, Location } from '@prisma/client'
import { createAiProvider } from '../providers/providerFactory'

/**
 * Clean and preprocess HTML content for AI processing
 */
export function preprocessHtml(html: string): string {
    // Remove script and style tags
    let cleaned = html.replace(
        /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        ''
    )
    cleaned = cleaned.replace(
        /<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi,
        ''
    )

    // Remove comments
    cleaned = cleaned.replace(/<!--[\s\S]*?-->/g, '')

    // Remove excessive whitespace
    cleaned = cleaned.replace(/\s+/g, ' ')

    // Remove empty lines
    cleaned = cleaned.replace(/^\s*[\r\n]/gm, '')

    return cleaned.trim()
}

/**
 * Generate instructions for the AI model to extract product information
 */
export function generateExtractionInstructions(
    customInstructions?: string
): string {
    const baseInstructions = `
You are a web scraping assistant. Extract product information from the provided HTML content and return it as a JSON array.

Each product should have the following structure:
{
    "name": "Product name (string, required)",
    "identifier": "Unique product identifier/URL (string, required)",
    "brand": "Brand name (string, required)",
    "currentPrice": "Price as number in cents (number, required, 0 if not available)",
    "currency": "Currency code (EUR|USD|GBP|CAD, required)",
    "materialType": "Material type (PLA|PETG|ABS|TPU|ASA|NYLON|OTHER, required)",
    "weight": "Weight in grams (number, required)",
    "color": "Color name (string, required)",
    "locations": "Array of locations ([Germany|UnitedStates|UnitedKingdom|Canada], required)"
}

Important rules:
1. Only extract 3D printing filament products
2. Price should be in cents (multiply by 100 if needed)
3. Use the product URL or a unique identifier as the "identifier" field
4. Extract color from product name or description
5. Determine material type from product name/description
6. If weight is not available, estimate based on typical filament weights (1000g for 1kg spools)
7. Return only valid JSON array, no additional text
8. If no products found, return empty array []

${customInstructions ? `Additional instructions: ${customInstructions}` : ''}
`

    return baseInstructions.trim()
}

/**
 * Extract products from HTML using the configured AI provider
 */
export async function extractProductsWithAi(
    html: string,
    config: AiScraperConfig
): Promise<CreateProduct[]> {
    const provider = createAiProvider(config)

    try {
        console.log(`[AI SCRAPER] Using ${provider.name} provider`)
        return await provider.extractProducts(html, config)
    } catch (error) {
        console.error(
            `[AI SCRAPER] Error with ${provider.name} provider:`,
            error
        )
        throw new Error(
            `Failed to extract products with ${provider.name}: ${error instanceof Error ? error.message : 'Unknown error'}`
        )
    }
}

/**
 * Parse and validate AI response
 */
export function parseAiResponse(response: string): CreateProduct[] {
    try {
        // Try to extract JSON from the response
        const jsonMatch = response.match(/\[[\s\S]*\]/)
        const jsonString = jsonMatch ? jsonMatch[0] : response

        const parsed = JSON.parse(jsonString)

        if (!Array.isArray(parsed)) {
            console.warn(
                '[AI SCRAPER] Response is not an array, wrapping in array'
            )
            return []
        }

        // Validate and transform each product
        const products: CreateProduct[] = []

        for (const item of parsed) {
            try {
                const product = validateAndTransformProduct(item)
                if (product) {
                    products.push(product)
                }
            } catch (error) {
                console.warn('[AI SCRAPER] Invalid product data:', item, error)
            }
        }

        return products
    } catch (error) {
        console.error('[AI SCRAPER] Failed to parse AI response:', error)
        console.error('[AI SCRAPER] Raw response:', response)
        return []
    }
}

/**
 * Validate and transform a single product from AI response
 */
function validateAndTransformProduct(item: any): CreateProduct | null {
    if (!item || typeof item !== 'object') {
        return null
    }

    // Required fields validation
    if (!item.name || !item.identifier || !item.brand) {
        console.warn('[AI SCRAPER] Missing required fields:', item)
        return null
    }

    // Validate currency
    const validCurrencies = Object.values(Currency)
    const currency = validCurrencies.includes(item.currency)
        ? item.currency
        : Currency.EUR

    // Validate material type
    const validMaterials = Object.values(MaterialType)
    const materialType = validMaterials.includes(item.materialType)
        ? item.materialType
        : MaterialType.OTHER

    // Validate locations
    const validLocations = Object.values(Location)
    let locations = Array.isArray(item.locations)
        ? item.locations.filter((loc: any) => validLocations.includes(loc))
        : [Location.Germany]

    if (locations.length === 0) {
        locations = [Location.Germany]
    }

    // Validate and convert price
    let currentPrice = 0
    if (typeof item.currentPrice === 'number' && item.currentPrice >= 0) {
        currentPrice = Math.round(item.currentPrice)
    } else if (typeof item.currentPrice === 'string') {
        const parsed = parseFloat(
            item.currentPrice.replace(/[^\d.,]/g, '').replace(',', '.')
        )
        if (!isNaN(parsed)) {
            currentPrice = Math.round(parsed * 100) // Convert to cents
        }
    }

    // Validate weight
    let weight = 1000 // Default 1kg
    if (typeof item.weight === 'number' && item.weight > 0) {
        weight = Math.round(item.weight)
    } else if (typeof item.weight === 'string') {
        const parsed = parseFloat(
            item.weight.replace(/[^\d.,]/g, '').replace(',', '.')
        )
        if (!isNaN(parsed)) {
            weight = Math.round(parsed)
        }
    }

    return {
        name: String(item.name).trim(),
        identifier: String(item.identifier).trim(),
        store: Store.threeDJake, // Default store, can be made configurable
        brand: String(item.brand).trim(),
        currentPrice,
        currency,
        materialType,
        weight,
        color: String(item.color || 'Unknown').trim(),
        locations
    }
}

/**
 * Test AI provider connection
 */
export async function testAiConnection(
    config: AiScraperConfig
): Promise<boolean> {
    try {
        const provider = createAiProvider(config)
        console.log(`[AI SCRAPER] Testing ${provider.name} connection`)
        return await provider.testConnection(config)
    } catch (error) {
        console.error('[AI SCRAPER] Connection test failed:', error)
        return false
    }
}

/**
 * @deprecated Use testAiConnection instead
 * Legacy function for backward compatibility
 */
export async function testOllamaConnection(
    config: AiScraperConfig
): Promise<boolean> {
    console.warn(
        '[AI SCRAPER] testOllamaConnection is deprecated, use testAiConnection instead'
    )
    return testAiConnection(config)
}
