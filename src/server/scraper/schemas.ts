import { z } from 'zod'
import { Currency, MaterialType, Store, Location } from '@prisma/client'

export const scraperOptionsSchema = z.object({
    headless: z.boolean().default(true),
    timeout: z.number().min(1000).max(60000).default(30000),
    retries: z.number().min(0).max(5).default(3),
    httpCredentials: z
        .object({
            username: z.string(),
            password: z.string()
        })
        .optional(),
    delayMin: z.number().min(0).max(10000).default(500),
    delayMax: z.number().min(0).max(20000).default(3000)
})

export const scrapeAndCreateProductSchema = z.object({
    name: z.string().min(1),
    identifier: z.string(),
    store: z.nativeEnum(Store),
    brand: z.string().min(1),
    currency: z.nativeEnum(Currency),
    materialType: z.nativeEnum(MaterialType),
    weight: z.number().positive(),
    color: z.string().min(1),
    locations: z.array(z.nativeEnum(Location)).min(1)
})

export const scrapeAndCreateParamsSchema = z.object({
    product: scrapeAndCreateProductSchema,
    options: scraperOptionsSchema.optional()
})

export const scrapeAllRequestSchema = z.object({
    options: scraperOptionsSchema.optional()
})

export const scrapeByIdSchema = z.object({
    id: z.string(),
    options: scraperOptionsSchema.optional()
})

export const aiProviderSchema = z.enum(['ollama', 'deepseek'])

export const aiScraperConfigSchema = z.object({
    provider: aiProviderSchema.default('ollama'),
    endpoint: z.string().url().default('http://**********:11434'),
    model: z.string().min(1).default('gemma3:12b'),
    instructions: z.string().optional(),
    maxTokens: z.number().min(1).max(8192).default(4096),
    temperature: z.number().min(0).max(1).default(0.1),
    apiKey: z.string().optional() // For providers that require API keys
})

export const aiScrapeUrlSchema = z.object({
    url: z.string().url(),
    config: aiScraperConfigSchema,
    options: scraperOptionsSchema.optional()
})

export type ScraperOptions = z.infer<typeof scraperOptionsSchema>
export type ScrapeAndCreateParams = z.infer<typeof scrapeAndCreateParamsSchema>
export type ScrapeAllRequest = z.infer<typeof scrapeAllRequestSchema>
export type ScrapeByIdRequest = z.infer<typeof scrapeByIdSchema>
export type AiScraperConfig = z.infer<typeof aiScraperConfigSchema>
export type AiScrapeUrlParams = z.infer<typeof aiScrapeUrlSchema>
