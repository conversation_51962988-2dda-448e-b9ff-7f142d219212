import { type AiProvider, type AiProviderType } from './aiProviderInterface'
import { OllamaProvider } from './ollamaProvider'
import { DeepSeekProvider } from './deepseekProvider'
import { type AiScraperConfig } from '../schemas'

/**
 * Factory for creating AI provider instances
 */
export class AiProviderFactory {
    private static providers = new Map<AiProviderType, () => AiProvider>([
        ['ollama', () => new OllamaProvider()],
        ['deepseek', () => new DeepSeekProvider()]
    ])

    /**
     * Create an AI provider instance based on the configuration
     */
    static createProvider(config: AiScraperConfig): AiProvider {
        const providerFactory = this.providers.get(config.provider)

        if (!providerFactory) {
            throw new Error(`Unsupported AI provider: ${config.provider}`)
        }

        return providerFactory()
    }

    /**
     * Get list of available providers
     */
    static getAvailableProviders(): AiProviderType[] {
        return Array.from(this.providers.keys())
    }

    /**
     * Check if a provider is supported
     */
    static isProviderSupported(provider: string): provider is AiProviderType {
        return this.providers.has(provider as AiProviderType)
    }

    /**
     * Register a new provider (for extensibility)
     */
    static registerProvider(
        type: AiProviderType,
        factory: () => AiProvider
    ): void {
        this.providers.set(type, factory)
    }
}

/**
 * Convenience function to create a provider
 */
export function createAiProvider(config: AiScraperConfig): AiProvider {
    return AiProviderFactory.createProvider(config)
}

/**
 * Default configurations for different providers
 * Note: temperature and maxTokens are intentionally omitted to use provider defaults
 */
export const defaultProviderConfigs = {
    ollama: {
        provider: 'ollama' as const,
        endpoint: 'http://**********:11434',
        model: 'gemma3:12b',
        instructions:
            'Focus on 3D printing filaments and materials. Extract all available product details including specifications.'
    },
    deepseek: {
        provider: 'deepseek' as const,
        endpoint: 'https://api.deepseek.com',
        model: 'deepseek-v3',
        instructions:
            'Focus on 3D printing filaments and materials. Extract all available product details including specifications.',
        apiKey: '' // Must be provided by user
    }
} as const
