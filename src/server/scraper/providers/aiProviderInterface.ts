import { type CreateProduct } from '../../product/schemas'
import { type AiScraperConfig } from '../schemas'

/**
 * Response from AI provider
 */
export interface AiProviderResponse {
    content: string
    usage?: {
        promptTokens?: number
        completionTokens?: number
        totalTokens?: number
    }
}

/**
 * Abstract interface for AI providers used in web scraping
 */
export interface AiProvider {
    /**
     * Name of the provider (e.g., 'Ollama', 'DeepSeek V3')
     */
    readonly name: string

    /**
     * Test if the provider is accessible and configured correctly
     */
    testConnection(config: AiScraperConfig): Promise<boolean>

    /**
     * Extract product information from HTML using AI
     */
    extractProducts(
        html: string,
        config: AiScraperConfig
    ): Promise<CreateProduct[]>

    /**
     * Generate a completion from the AI provider
     * This is the low-level method that other methods can use
     */
    generateCompletion(
        prompt: string,
        config: AiScraperConfig
    ): Promise<AiProviderResponse>
}

/**
 * Configuration for different AI providers
 */
export interface ProviderConfig {
    ollama: {
        endpoint: string
        model: string
        temperature?: number
        maxTokens?: number
    }
    deepseek: {
        endpoint: string
        model: string
        apiKey: string
        temperature?: number
        maxTokens?: number
    }
}

/**
 * Provider type enum
 */
export type AiProviderType = 'ollama' | 'deepseek'
