import { <PERSON>llama } from 'ollama'
import { type AiProvider, type AiProviderResponse } from './aiProviderInterface'
import { type AiScraperConfig } from '../schemas'
import { type CreateProduct } from '../../product/schemas'
import {
    preprocessHtml,
    generateExtractionInstructions,
    parseAiResponse
} from '../utils/aiScraper'

/**
 * Ollama AI provider implementation
 */
export class OllamaProvider implements AiProvider {
    readonly name = 'Ollama'

    /**
     * Test connection to Ollama server
     */
    async testConnection(config: AiScraperConfig): Promise<boolean> {
        try {
            const ollama = new Ollama({ host: config.endpoint })
            await ollama.list()
            return true
        } catch (error) {
            console.error('[OLLAMA PROVIDER] Connection test failed:', error)
            return false
        }
    }

    /**
     * Extract products from HTML using Ollama
     */
    async extractProducts(
        html: string,
        config: AiScraperConfig
    ): Promise<CreateProduct[]> {
        const cleanedHtml = preprocessHtml(html)
        const instructions = generateExtractionInstructions(config.instructions)

        const prompt = `${instructions}

HTML Content:
${cleanedHtml}`

        const response = await this.generateCompletion(prompt, config)
        return parseAiResponse(response.content)
    }

    /**
     * Generate completion using Ollama
     */
    async generateCompletion(
        prompt: string,
        config: AiScraperConfig
    ): Promise<AiProviderResponse> {
        try {
            const ollama = new Ollama({ host: config.endpoint })

            const options: Record<string, any> = {}

            // Only set temperature if explicitly configured
            if (config.temperature !== undefined) {
                options.temperature = config.temperature
            }

            // Only set maxTokens if explicitly configured
            if (config.maxTokens !== undefined) {
                options.num_predict = config.maxTokens
            }

            const response = await ollama.generate({
                model: config.model,
                prompt,
                options,
                stream: false
            })

            return {
                content: response.response,
                usage: {
                    promptTokens: response.prompt_eval_count,
                    completionTokens: response.eval_count,
                    totalTokens: (response.prompt_eval_count || 0) + (response.eval_count || 0)
                }
            }
        } catch (error) {
            console.error('[OLLAMA PROVIDER] Error generating completion:', error)
            throw new Error(
                `Failed to generate completion with Ollama: ${error instanceof Error ? error.message : 'Unknown error'}`
            )
        }
    }
}
