import { type AiProvider, type Ai<PERSON>roviderResponse } from './aiProviderInterface'
import { type AiScraperConfig } from '../schemas'
import { type CreateProduct } from '../../product/schemas'
import {
    preprocessHtml,
    generateExtractionInstructions,
    parseAiResponse
} from '../utils/aiScraper'

/**
 * DeepSeek V3 AI provider implementation
 * Uses OpenAI-compatible API format
 */
export class DeepSeekProvider implements AiProvider {
    readonly name = 'DeepSeek V3'

    /**
     * Test connection to DeepSeek API
     */
    async testConnection(config: AiScraperConfig): Promise<boolean> {
        try {
            if (!config.apiKey) {
                console.error('[DEEPSEEK PROVIDER] API key is required')
                return false
            }

            // Test with a simple completion request
            const response = await fetch(`${config.endpoint}/v1/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bear<PERSON> ${config.apiKey}`
                },
                body: JSON.stringify({
                    model: config.model,
                    messages: [
                        {
                            role: 'user',
                            content: 'Hello, this is a connection test.'
                        }
                    ],
                    max_tokens: 10
                })
            })

            return response.ok
        } catch (error) {
            console.error('[DEEPSEEK PROVIDER] Connection test failed:', error)
            return false
        }
    }

    /**
     * Extract products from HTML using DeepSeek V3
     */
    async extractProducts(
        html: string,
        config: AiScraperConfig
    ): Promise<CreateProduct[]> {
        const cleanedHtml = preprocessHtml(html)
        const instructions = generateExtractionInstructions(config.instructions)

        const prompt = `${instructions}

HTML Content:
${cleanedHtml}`

        const response = await this.generateCompletion(prompt, config)
        return parseAiResponse(response.content)
    }

    /**
     * Generate completion using DeepSeek V3 API
     */
    async generateCompletion(
        prompt: string,
        config: AiScraperConfig
    ): Promise<AiProviderResponse> {
        try {
            if (!config.apiKey) {
                throw new Error('API key is required for DeepSeek provider')
            }

            const requestBody: any = {
                model: config.model,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ]
            }

            // Only set temperature if explicitly configured
            if (config.temperature !== undefined) {
                requestBody.temperature = config.temperature
            }

            // Only set maxTokens if explicitly configured
            if (config.maxTokens !== undefined) {
                requestBody.max_tokens = config.maxTokens
            }

            const response = await fetch(`${config.endpoint}/v1/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${config.apiKey}`
                },
                body: JSON.stringify(requestBody)
            })

            if (!response.ok) {
                const errorText = await response.text()
                throw new Error(`DeepSeek API error: ${response.status} ${errorText}`)
            }

            const data = await response.json()

            if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                throw new Error('Invalid response format from DeepSeek API')
            }

            return {
                content: data.choices[0].message.content,
                usage: {
                    promptTokens: data.usage?.prompt_tokens,
                    completionTokens: data.usage?.completion_tokens,
                    totalTokens: data.usage?.total_tokens
                }
            }
        } catch (error) {
            console.error('[DEEPSEEK PROVIDER] Error generating completion:', error)
            throw new Error(
                `Failed to generate completion with DeepSeek: ${error instanceof Error ? error.message : 'Unknown error'}`
            )
        }
    }
}
