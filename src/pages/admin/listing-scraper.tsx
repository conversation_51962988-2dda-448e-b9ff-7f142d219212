import { useState } from 'react'
import {
    <PERSON><PERSON>,
    <PERSON>,
    Container,
    <PERSON>lex,
    <PERSON><PERSON>,
    Stack,
    Text,
    createToaster
} from '@chakra-ui/react'
import { api } from '~/utils/api'
import { Store } from '@prisma/client'

const toaster = createToaster({
    placement: 'top-end'
})

export default function ListingScraperAdmin() {
    const [selectedStore, setSelectedStore] = useState<Store | ''>('')

    // API mutations
    const scrapeAllListingsMutation =
        api.listingScraper.scrapeAllListings.useMutation({
            onSuccess: (data) => {
                showToast({
                    title: 'Success',
                    description: `Found ${data.reduce((sum, result) => sum + result.productsFound, 0)} products, added ${data.reduce((sum, result) => sum + result.productsAdded, 0)} new products`,
                    status: 'success'
                })
            },
            onError: (error) => {
                showToast({
                    title: 'Error',
                    description: error.message,
                    status: 'error'
                })
            }
        })

    const scrapeStoreListingsMutation =
        api.listingScraper.scrapeStoreListings.useMutation({
            onSuccess: (data) => {
                showToast({
                    title: 'Success',
                    description: `Found ${data.productsFound} products, added ${data.productsAdded} new products`,
                    status: 'success'
                })
            },
            onError: (error) => {
                showToast({
                    title: 'Error',
                    description: error.message,
                    status: 'error'
                })
            }
        })

    const queueAllStoresMutation =
        api.listingScraper.queueAllStores.useMutation({
            onSuccess: (count) => {
                showToast({
                    title: 'Success',
                    description: `Queued ${count} stores for scraping`,
                    status: 'success'
                })
            },
            onError: (error) => {
                showToast({
                    title: 'Error',
                    description: error.message,
                    status: 'error'
                })
            }
        })

    const queueStoreMutation = api.listingScraper.queueStore.useMutation({
        onSuccess: (result) => {
            showToast({
                title: result.added ? 'Success' : 'Info',
                description: result.message,
                status: result.added ? 'success' : 'info'
            })
        },
        onError: (error) => {
            showToast({
                title: 'Error',
                description: error.message,
                status: 'error'
            })
        }
    })

    const processNextJobMutation =
        api.listingScraper.processNextJob.useMutation({
            onSuccess: (result) => {
                if (result) {
                    showToast({
                        title: 'Success',
                        description: `Processed job for ${result.store}. Found ${result.productsFound} products, added ${result.productsAdded} new products`,
                        status: 'success'
                    })
                } else {
                    showToast({
                        title: 'Info',
                        description: 'No jobs in queue or job failed',
                        status: 'info'
                    })
                }
            },
            onError: (error) => {
                showToast({
                    title: 'Error',
                    description: error.message,
                    status: 'error'
                })
            }
        })

    const resetStalledJobsMutation =
        api.listingScraper.resetStalledJobs.useMutation({
            onSuccess: (result) => {
                showToast({
                    title: 'Success',
                    description: `Reset ${result.count} stalled jobs`,
                    status: 'success'
                })
            },
            onError: (error) => {
                showToast({
                    title: 'Error',
                    description: error.message,
                    status: 'error'
                })
            }
        })

    // Get queue stats
    const { data: queueStats = [], refetch: refetchQueueStats } =
        api.listingScraper.getQueueStats.useQuery()

    // Helper function for showing toast messages
    const showToast = ({
        title,
        description,
        status
    }: {
        title: string
        description: string
        status: 'info' | 'success' | 'error' | 'warning'
    }) => {
        toaster.create({
            title,
            description,
            type: status,
            duration: 5000
        })
    }

    // Handle scraping all listings
    const handleScrapeAllListings = () => {
        scrapeAllListingsMutation.mutate()
    }

    // Handle scraping a specific store's listings
    const handleScrapeStoreListings = () => {
        if (!selectedStore) {
            showToast({
                title: 'Error',
                description: 'Please select a store',
                status: 'error'
            })
            return
        }

        scrapeStoreListingsMutation.mutate({ store: selectedStore as Store })
    }

    // Handle queueing all stores
    const handleQueueAllStores = () => {
        queueAllStoresMutation.mutate({ priority: 1 })
    }

    // Handle queueing a specific store
    const handleQueueStore = () => {
        if (!selectedStore) {
            showToast({
                title: 'Error',
                description: 'Please select a store',
                status: 'error'
            })
            return
        }

        queueStoreMutation.mutate({
            store: selectedStore as Store,
            priority: 1
        })
    }

    // Handle processing the next job
    const handleProcessNextJob = () => {
        processNextJobMutation.mutate({
            workerId: `manual-${Date.now()}`
        })
    }

    // Handle resetting stalled jobs
    const handleResetStalledJobs = () => {
        resetStalledJobsMutation.mutate({ stalledMinutes: 30 })
    }

    return (
        <Container maxW="container.lg" py={8}>
            <Heading mb={6}>Listing Scraper Admin</Heading>

            <Stack gap={6}>
                {/* Direct Scraping Section */}
                <Card.Root variant="outline">
                    <Card.Body>
                        <Heading size="md" mb={4}>
                            Direct Scraping
                        </Heading>
                        <Stack gap={4}>
                            <Flex gap={4} alignItems="center">
                                <select
                                    value={selectedStore}
                                    onChange={(e) =>
                                        setSelectedStore(
                                            e.target.value as Store
                                        )
                                    }
                                    style={{
                                        width: '200px',
                                        padding: '8px',
                                        borderRadius: '6px',
                                        border: '1px solid #e2e8f0'
                                    }}
                                >
                                    <option value="">Select store</option>
                                    {Object.values(Store).map((store) => (
                                        <option key={store} value={store}>
                                            {store}
                                        </option>
                                    ))}
                                </select>
                                <Button
                                    colorScheme="blue"
                                    onClick={handleScrapeStoreListings}
                                    loading={
                                        scrapeStoreListingsMutation.isPending
                                    }
                                >
                                    Scrape Store Listings
                                </Button>
                            </Flex>
                            <Button
                                colorScheme="blue"
                                onClick={handleScrapeAllListings}
                                loading={scrapeAllListingsMutation.isPending}
                            >
                                Scrape All Listings
                            </Button>
                        </Stack>
                    </Card.Body>
                </Card.Root>

                {/* Queue Management Section */}
                <Card.Root variant="outline">
                    <Card.Body>
                        <Heading size="md" mb={4}>
                            Queue Management
                        </Heading>
                        <Stack gap={4}>
                            <Flex gap={4} alignItems="center">
                                <select
                                    value={selectedStore}
                                    onChange={(e) =>
                                        setSelectedStore(
                                            e.target.value as Store
                                        )
                                    }
                                    style={{
                                        width: '200px',
                                        padding: '8px',
                                        borderRadius: '6px',
                                        border: '1px solid #e2e8f0'
                                    }}
                                >
                                    <option value="">Select store</option>
                                    {Object.values(Store).map((store) => (
                                        <option key={store} value={store}>
                                            {store}
                                        </option>
                                    ))}
                                </select>
                                <Button
                                    colorScheme="green"
                                    onClick={handleQueueStore}
                                    loading={queueStoreMutation.isPending}
                                >
                                    Queue Store
                                </Button>
                            </Flex>
                            <Button
                                colorScheme="green"
                                onClick={handleQueueAllStores}
                                loading={queueAllStoresMutation.isPending}
                            >
                                Queue All Stores
                            </Button>
                            <Button
                                colorScheme="purple"
                                onClick={handleProcessNextJob}
                                loading={processNextJobMutation.isPending}
                            >
                                Process Next Job
                            </Button>
                            <Button
                                colorScheme="orange"
                                onClick={handleResetStalledJobs}
                                loading={resetStalledJobsMutation.isPending}
                            >
                                Reset Stalled Jobs
                            </Button>
                        </Stack>
                    </Card.Body>
                </Card.Root>

                {/* Queue Stats Section */}
                <Card.Root variant="outline">
                    <Card.Body>
                        <Flex
                            justifyContent="space-between"
                            alignItems="center"
                            mb={4}
                        >
                            <Heading size="md">Queue Statistics</Heading>
                            <Button
                                size="sm"
                                onClick={() => void refetchQueueStats()}
                                variant="outline"
                            >
                                Refresh
                            </Button>
                        </Flex>
                        <Stack gap={2}>
                            {queueStats.length === 0 ? (
                                <Text>No queue statistics available</Text>
                            ) : (
                                queueStats.map((stat) => (
                                    <Flex
                                        key={stat.status}
                                        justifyContent="space-between"
                                    >
                                        <Text fontWeight="bold">
                                            {stat.status}:
                                        </Text>
                                        <Text>{stat.count}</Text>
                                    </Flex>
                                ))
                            )}
                        </Stack>
                    </Card.Body>
                </Card.Root>
            </Stack>
        </Container>
    )
}
