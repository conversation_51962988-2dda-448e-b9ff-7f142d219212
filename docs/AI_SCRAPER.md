# AI-Powered Generic Scraper

The AI-powered generic scraper uses configurable AI providers to extract product information from any webpage. Unlike traditional scrapers that rely on specific CSS selectors, this scraper uses AI to understand and extract product data from HTML content.

## Features

- **Generic scraping**: Works with any webpage containing 3D printing filament products
- **Multiple AI providers**: Support for Ollama (local) and DeepSeek V3 (cloud)
- **AI-powered extraction**: Uses large language models to understand and extract product information
- **Configurable**: Supports custom instructions and different AI models
- **Rate limiting**: Built-in delays and anti-detection measures
- **Batch processing**: Can scrape multiple URLs with the same or different configurations
- **Database integration**: Automatically saves extracted products to the database
- **Provider switching**: Easy switching between local and cloud AI providers

## AI Providers

The scraper supports multiple AI providers, each with different characteristics:

### Ollama (Local)

- **Pros**: Free, private, no API costs, works offline
- **Cons**: Requires local setup, uses system resources
- **Best for**: Development, privacy-sensitive use cases, cost control

### DeepSeek V3 (Cloud)

- **Pros**: No local setup, high performance, latest models
- **Cons**: API costs, requires internet, data sent to external service
- **Best for**: Production, high-volume scraping, latest AI capabilities

## Prerequisites

### For Ollama Provider

#### 1. Install Ollama

Download and install Ollama from [https://ollama.ai](https://ollama.ai)

#### 2. Start Ollama Server

```bash
ollama serve
```

#### 3. Pull a Model

```bash
# Recommended models for product extraction
ollama pull llama3.2        # Good balance of speed and accuracy
ollama pull mistral         # Alternative option
ollama pull codellama       # Good for structured data
```

### For DeepSeek V3 Provider

#### 1. Get API Key

Sign up at [https://platform.deepseek.com](https://platform.deepseek.com) and get your API key.

#### 2. Set Environment Variable

```bash
export DEEPSEEK_API_KEY="your-api-key-here"
```

## Usage

### CLI Testing

Test the AI scraper using the command line:

```bash
# Test connection to Ollama
pnpm ai-scraper test-connection

# Test with custom endpoint and model
pnpm ai-scraper test-connection http://localhost:11434 mistral

# Scrape a single URL
pnpm ai-scraper scrape-url https://www.3djake.de/filament/pet-filament

# Scrape with custom configuration
pnpm ai-scraper scrape-url https://example.com http://localhost:11434 mistral
```

### API Endpoints

The AI scraper provides several tRPC endpoints:

#### 1. Test Connection

```typescript
// Test if Ollama server is accessible
await api.listingScraper.testAiConnection.mutate({
    endpoint: 'http://localhost:11434',
    model: 'llama3.2'
})
```

#### 2. Scrape Single URL

```typescript
// Extract products without saving to database
const products = await api.listingScraper.aiScrapeUrl.mutate({
    url: 'https://www.3djake.de/filament/pet-filament',
    config: {
        endpoint: 'http://localhost:11434',
        model: 'llama3.2',
        temperature: 0.1,
        maxTokens: 4096,
        instructions:
            'Focus on 3D printing filaments. Extract all product details.'
    },
    options: {
        headless: true,
        timeout: 30000
    }
})
```

#### 3. Scrape and Save

```typescript
// Extract products and save to database
const result = await api.listingScraper.aiScrapeAndSave.mutate({
    url: 'https://www.3djake.de/filament/pet-filament',
    config: {
        endpoint: 'http://localhost:11434',
        model: 'llama3.2'
    }
})

console.log(`Found: ${result.productsFound}, Saved: ${result.productsSaved}`)
```

#### 4. Batch Scraping

```typescript
// Scrape multiple URLs with the same configuration
const result = await api.listingScraper.aiScrapeMultipleUrls.mutate({
    urls: [
        'https://www.3djake.de/filament/pet-filament',
        'https://www.3djake.de/filament/pla-filament'
    ],
    config: {
        endpoint: 'http://localhost:11434',
        model: 'llama3.2'
    }
})
```

## Configuration

### AI Scraper Config

```typescript
type AiScraperConfig = {
    provider: 'ollama' | 'deepseek' // AI provider to use (default: 'ollama')
    endpoint: string // Provider endpoint URL
    model: string // Model name
    instructions?: string // Custom extraction instructions
    maxTokens?: number // Maximum response tokens (default: 4096)
    temperature?: number // Response randomness 0-1 (default: 0.1)
    apiKey?: string // API key for cloud providers (required for DeepSeek)
}
```

### Provider-Specific Examples

#### Ollama Configuration

```typescript
const ollamaConfig = {
    provider: 'ollama',
    endpoint: 'http://localhost:11434',
    model: 'llama3.2', // or 'mistral', 'codellama', etc.
    temperature: 0.1,
    maxTokens: 4096
}
```

#### DeepSeek V3 Configuration

```typescript
const deepseekConfig = {
    provider: 'deepseek',
    endpoint: 'https://api.deepseek.com',
    model: 'deepseek-v3',
    apiKey: 'your-api-key-here',
    temperature: 0.1,
    maxTokens: 4096
}
```

### Custom Instructions

You can provide custom instructions to guide the AI's extraction process:

```typescript
const config = {
    provider: 'ollama', // or 'deepseek'
    endpoint: 'http://localhost:11434',
    model: 'llama3.2',
    instructions: `
    Extract only premium filament products.
    Focus on products with detailed specifications.
    Include any special features or certifications.
    Prioritize products with clear weight and price information.
  `
}
```

## Extracted Product Format

The AI scraper extracts products in this format:

```typescript
type CreateProduct = {
    name: string // Product name
    identifier: string // Unique identifier (usually the product URL)
    store: Store // Store enum (defaults to threeDJake)
    brand: string // Brand name
    currentPrice: number // Price in cents
    currency: Currency // Currency enum (EUR, USD, GBP, CAD)
    materialType: MaterialType // Material enum (PLA, PETG, ABS, etc.)
    weight: number // Weight in grams
    color: string // Color name
    locations: Location[] // Array of location enums
}
```

## Best Practices

### 1. Provider Selection

#### When to use Ollama:

- Development and testing
- Privacy-sensitive applications
- Cost control (no API fees)
- Offline operation required
- Full control over model and infrastructure

#### When to use DeepSeek V3:

- Production environments
- High-volume scraping
- Need for latest AI capabilities
- No local infrastructure available
- Consistent performance requirements

### 2. Model Selection

#### Ollama Models:

- **llama3.2**: Good balance of speed and accuracy
- **mistral**: Faster but may be less accurate for complex pages
- **codellama**: Better for structured data extraction

#### DeepSeek Models:

- **deepseek-v3**: Latest model with excellent reasoning capabilities

### 3. Temperature Settings

- **0.1**: More consistent, deterministic results (recommended)
- **0.3**: Slightly more creative but still reliable
- **0.7+**: More creative but less consistent

### 4. Custom Instructions

- Be specific about what to extract
- Mention the product category (3D printing filaments)
- Specify any quality requirements
- Include formatting preferences

### 5. Error Handling

- Always test connection before scraping
- Handle partial failures gracefully
- Monitor extraction quality and adjust instructions

## Troubleshooting

### Ollama Connection Issues

```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# Restart Ollama service
ollama serve
```

### Ollama Model Issues

```bash
# List available models
ollama list

# Pull missing model
ollama pull llama3.2

# Remove and re-pull corrupted model
ollama rm llama3.2
ollama pull llama3.2
```

### DeepSeek Connection Issues

```bash
# Test API key and endpoint
curl -X POST "https://api.deepseek.com/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{"model":"deepseek-v3","messages":[{"role":"user","content":"test"}],"max_tokens":10}'
```

Common DeepSeek issues:

- **Invalid API key**: Check your API key is correct and active
- **Rate limiting**: Reduce request frequency or upgrade plan
- **Model not found**: Verify model name is correct (e.g., 'deepseek-v3')
- **Network issues**: Check internet connection and firewall settings

### Extraction Quality Issues

1. **No products found**: Check if the page actually contains filament products
2. **Incorrect data**: Refine the custom instructions
3. **Partial extraction**: Increase maxTokens or use a more capable model
4. **Inconsistent results**: Lower the temperature setting

## Performance Considerations

- **Model size**: Larger models are more accurate but slower
- **Token limits**: Higher limits allow more detailed extraction but take longer
- **Batch size**: Process URLs in smaller batches to avoid timeouts
- **Rate limiting**: Built-in delays prevent being blocked by websites

## Security Notes

- The scraper only extracts product information, no sensitive data
- HTML content is preprocessed to remove scripts and styles
- All network requests go through the stealth browser setup

### Provider-Specific Security Considerations

#### Ollama:

- Runs locally, no data is sent to external AI services
- Full control over data processing and storage
- No API key management required

#### DeepSeek V3:

- Data is sent to DeepSeek's servers for processing
- API key should be kept secure and not exposed in client-side code
- Consider data privacy implications for sensitive content
- Monitor API usage to prevent unexpected costs
