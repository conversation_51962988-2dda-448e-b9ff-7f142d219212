meta {
  name: AI Scrape URL (DeepSeek V3)
  type: http
  seq: 9
}

post {
  url: {{baseUrl}}/api/trpc/listingScraper.aiScrapeUrl
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "url": "https://www.3djake.de/filament/pet-filament",
    "config": {
      "provider": "deepseek",
      "endpoint": "https://api.deepseek.com",
      "model": "deepseek-v3",
      "temperature": 0.1,
      "maxTokens": 4096,
      "apiKey": "{{deepseekApiKey}}",
      "instructions": "Focus on 3D printing filaments. Extract product name, brand, price, material type, weight, and color."
    },
    "options": {
      "headless": true,
      "timeout": 30000
    }
  }
}

docs {
  # AI Scrape URL (DeepSeek V3 Provider)
  
  Scrape a single URL using DeepSeek V3 AI provider to extract product information without saving to database.
  
  ## Request Body
  
  - `url`: (string) URL to scrape
  - `config`: AI scraper configuration for DeepSeek V3
    - `provider`: (string) "deepseek" - AI provider to use
    - `endpoint`: (string) DeepSeek API endpoint URL (typically "https://api.deepseek.com")
    - `model`: (string) Model name to use (e.g., "deepseek-v3")
    - `temperature`: (number, optional, default: 0.1) Temperature for response generation (0-1)
    - `maxTokens`: (number, optional, default: 4096) Maximum tokens for the response
    - `apiKey`: (string) DeepSeek API key (required)
    - `instructions`: (string, optional) Custom instructions for product extraction
  - `options`: (optional) Scraper options
    - `headless`: (boolean, default: true) Whether to run the browser in headless mode
    - `timeout`: (number, default: 30000) Timeout in milliseconds
    - `retries`: (number, default: 3) Number of retries on failure
  
  ## Prerequisites
  
  1. DeepSeek API key must be set in environment variables
  2. Set `deepseekApiKey` variable in Bruno environment
  
  ## Environment Variables
  
  Add to your Bruno environment file:
  ```json
  {
    "deepseekApiKey": "your-deepseek-api-key-here"
  }
  ```
  
  ## Response
  
  Returns an array of extracted product data objects.
  
  ## Example Response
  
  ```json
  {
    "result": {
      "data": [
        {
          "name": "PETG Filament Red",
          "identifier": "https://example.com/product/123",
          "store": "threeDJake",
          "brand": "ExampleBrand",
          "currentPrice": 2499,
          "currency": "EUR",
          "materialType": "PETG",
          "weight": 1000,
          "color": "Red",
          "locations": ["Germany"]
        }
      ]
    }
  }
  ```
  
  ## Cost Considerations
  
  DeepSeek V3 is a cloud-based API service that charges per token usage. Monitor your usage to avoid unexpected costs.
}
